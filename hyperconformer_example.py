#!/usr/bin/env python3
"""
HyperConformer: Multi-head HyperMixer for Efficient Speech Recognition
Complete runnable Python implementation example

This implementation is based on the HyperConformer architecture described in:
"HyperConformer: Multi-head HyperMixer for Efficient Speech Recognition"
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import numpy as np
from typing import Optional, Tuple


class PositionalEncoding(nn.Module):
    """Positional encoding for transformer-like architectures."""
    
    def __init__(self, d_model: int, max_len: int = 5000):
        super().__init__()
        
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        
        self.register_buffer('pe', pe)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        return x + self.pe[:x.size(0), :]


class MultiHeadAttention(nn.Module):
    """Multi-head self-attention mechanism."""
    
    def __init__(self, d_model: int, num_heads: int, dropout: float = 0.1):
        super().__init__()
        assert d_model % num_heads == 0
        
        self.d_model = d_model
        self.num_heads = num_heads
        self.d_k = d_model // num_heads
        
        self.w_q = nn.Linear(d_model, d_model)
        self.w_k = nn.Linear(d_model, d_model)
        self.w_v = nn.Linear(d_model, d_model)
        self.w_o = nn.Linear(d_model, d_model)
        
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x: torch.Tensor, mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        batch_size, seq_len, d_model = x.size()
        
        # Linear projections
        Q = self.w_q(x).view(batch_size, seq_len, self.num_heads, self.d_k).transpose(1, 2)
        K = self.w_k(x).view(batch_size, seq_len, self.num_heads, self.d_k).transpose(1, 2)
        V = self.w_v(x).view(batch_size, seq_len, self.num_heads, self.d_k).transpose(1, 2)
        
        # Attention
        scores = torch.matmul(Q, K.transpose(-2, -1)) / math.sqrt(self.d_k)
        
        if mask is not None:
            scores = scores.masked_fill(mask == 0, -1e9)
        
        attn_weights = F.softmax(scores, dim=-1)
        attn_weights = self.dropout(attn_weights)
        
        context = torch.matmul(attn_weights, V)
        context = context.transpose(1, 2).contiguous().view(
            batch_size, seq_len, d_model)
        
        return self.w_o(context)


class HyperMixer(nn.Module):
    """HyperMixer module - core component of HyperConformer."""
    
    def __init__(self, d_model: int, expansion_factor: int = 4, dropout: float = 0.1):
        super().__init__()
        self.d_model = d_model
        self.d_ff = d_model * expansion_factor
        
        # HyperMixer components
        self.hyper_net = nn.Sequential(
            nn.Linear(d_model, self.d_ff),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(self.d_ff, d_model * 2)  # Generate weights for mixing
        )
        
        self.mixer = nn.Sequential(
            nn.Linear(d_model, self.d_ff),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(self.d_ff, d_model)
        )
        
        self.layer_norm = nn.LayerNorm(d_model)
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # Generate mixing weights using hypernetwork
        hyper_weights = self.hyper_net(x.mean(dim=1, keepdim=True))  # Global context
        weight_1, weight_2 = hyper_weights.chunk(2, dim=-1)
        
        # Apply mixing with generated weights
        mixed = self.mixer(x)
        output = weight_1 * x + weight_2 * mixed
        
        return self.layer_norm(output)


class ConvolutionModule(nn.Module):
    """Convolution module for local feature extraction."""
    
    def __init__(self, d_model: int, kernel_size: int = 31, dropout: float = 0.1):
        super().__init__()
        
        self.layer_norm = nn.LayerNorm(d_model)
        self.pointwise_conv1 = nn.Conv1d(d_model, d_model * 2, 1)
        self.glu = nn.GLU(dim=1)
        self.depthwise_conv = nn.Conv1d(
            d_model, d_model, kernel_size, 
            padding=(kernel_size - 1) // 2, groups=d_model
        )
        self.batch_norm = nn.BatchNorm1d(d_model)
        self.swish = nn.SiLU()
        self.pointwise_conv2 = nn.Conv1d(d_model, d_model, 1)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # x: (batch, seq_len, d_model)
        x = self.layer_norm(x)
        x = x.transpose(1, 2)  # (batch, d_model, seq_len)
        
        x = self.pointwise_conv1(x)
        x = self.glu(x)
        x = self.depthwise_conv(x)
        x = self.batch_norm(x)
        x = self.swish(x)
        x = self.pointwise_conv2(x)
        x = self.dropout(x)
        
        return x.transpose(1, 2)  # (batch, seq_len, d_model)


class HyperConformerBlock(nn.Module):
    """Single HyperConformer block combining attention, convolution, and HyperMixer."""
    
    def __init__(
        self, 
        d_model: int, 
        num_heads: int, 
        conv_kernel_size: int = 31,
        expansion_factor: int = 4,
        dropout: float = 0.1
    ):
        super().__init__()
        
        # Feed-forward module 1
        self.ff1 = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Linear(d_model, d_model * expansion_factor),
            nn.SiLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model * expansion_factor, d_model),
            nn.Dropout(dropout)
        )
        
        # Multi-head self-attention
        self.mhsa = MultiHeadAttention(d_model, num_heads, dropout)
        self.mhsa_norm = nn.LayerNorm(d_model)
        
        # Convolution module
        self.conv = ConvolutionModule(d_model, conv_kernel_size, dropout)
        
        # HyperMixer module
        self.hyper_mixer = HyperMixer(d_model, expansion_factor, dropout)
        
        # Feed-forward module 2
        self.ff2 = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Linear(d_model, d_model * expansion_factor),
            nn.SiLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model * expansion_factor, d_model),
            nn.Dropout(dropout)
        )
        
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x: torch.Tensor, mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        # Feed-forward 1 (half step)
        x = x + 0.5 * self.ff1(x)
        
        # Multi-head self-attention
        attn_out = self.mhsa(self.mhsa_norm(x), mask)
        x = x + self.dropout(attn_out)
        
        # Convolution module
        conv_out = self.conv(x)
        x = x + self.dropout(conv_out)
        
        # HyperMixer module
        mixer_out = self.hyper_mixer(x)
        x = x + self.dropout(mixer_out)
        
        # Feed-forward 2 (half step)
        x = x + 0.5 * self.ff2(x)
        
        return x


class HyperConformer(nn.Module):
    """Complete HyperConformer model for speech recognition."""
    
    def __init__(
        self,
        input_dim: int = 80,  # Mel-spectrogram features
        d_model: int = 512,
        num_layers: int = 12,
        num_heads: int = 8,
        conv_kernel_size: int = 31,
        expansion_factor: int = 4,
        num_classes: int = 1000,  # Vocabulary size
        dropout: float = 0.1,
        max_len: int = 5000
    ):
        super().__init__()
        
        self.d_model = d_model
        
        # Input projection
        self.input_projection = nn.Linear(input_dim, d_model)
        
        # Positional encoding
        self.pos_encoding = PositionalEncoding(d_model, max_len)
        
        # HyperConformer blocks
        self.blocks = nn.ModuleList([
            HyperConformerBlock(
                d_model=d_model,
                num_heads=num_heads,
                conv_kernel_size=conv_kernel_size,
                expansion_factor=expansion_factor,
                dropout=dropout
            ) for _ in range(num_layers)
        ])
        
        # Output layers
        self.layer_norm = nn.LayerNorm(d_model)
        self.output_projection = nn.Linear(d_model, num_classes)
        
        self.dropout = nn.Dropout(dropout)
        
    def forward(
        self, 
        x: torch.Tensor, 
        mask: Optional[torch.Tensor] = None
    ) -> torch.Tensor:
        """
        Args:
            x: Input tensor of shape (batch_size, seq_len, input_dim)
            mask: Optional attention mask
            
        Returns:
            Output tensor of shape (batch_size, seq_len, num_classes)
        """
        # Input projection and positional encoding
        x = self.input_projection(x) * math.sqrt(self.d_model)
        x = self.pos_encoding(x)
        x = self.dropout(x)
        
        # Pass through HyperConformer blocks
        for block in self.blocks:
            x = block(x, mask)
        
        # Output projection
        x = self.layer_norm(x)
        x = self.output_projection(x)
        
        return x


def create_padding_mask(lengths: torch.Tensor, max_len: int) -> torch.Tensor:
    """Create padding mask for variable length sequences."""
    batch_size = lengths.size(0)
    mask = torch.arange(max_len).expand(batch_size, max_len) < lengths.unsqueeze(1)
    return mask.unsqueeze(1).unsqueeze(1)  # (batch, 1, 1, seq_len)


def main():
    """Example usage of HyperConformer model."""
    
    # Model configuration
    config = {
        'input_dim': 80,      # Mel-spectrogram features
        'd_model': 512,       # Model dimension
        'num_layers': 12,     # Number of HyperConformer blocks
        'num_heads': 8,       # Number of attention heads
        'conv_kernel_size': 31,  # Convolution kernel size
        'expansion_factor': 4,   # Feed-forward expansion factor
        'num_classes': 1000,     # Vocabulary size
        'dropout': 0.1,          # Dropout rate
        'max_len': 5000          # Maximum sequence length
    }
    
    # Create model
    model = HyperConformer(**config)
    
    # Example input (batch_size=2, seq_len=100, input_dim=80)
    batch_size, seq_len, input_dim = 2, 100, 80
    x = torch.randn(batch_size, seq_len, input_dim)
    
    # Sequence lengths for padding mask
    lengths = torch.tensor([100, 80])  # Second sequence is shorter
    mask = create_padding_mask(lengths, seq_len)
    
    # Forward pass
    print("Input shape:", x.shape)
    print("Model parameters:", sum(p.numel() for p in model.parameters()))
    
    with torch.no_grad():
        output = model(x, mask)
        print("Output shape:", output.shape)
        print("Output sample:", output[0, 0, :10])  # First 10 logits of first sample
    
    # Training example
    print("\n--- Training Example ---")
    model.train()
    optimizer = torch.optim.Adam(model.parameters(), lr=1e-4)
    criterion = nn.CrossEntropyLoss(ignore_index=-1)
    
    # Dummy target (batch_size, seq_len)
    target = torch.randint(0, config['num_classes'], (batch_size, seq_len))
    
    # Forward pass
    output = model(x, mask)
    
    # Compute loss
    loss = criterion(output.view(-1, config['num_classes']), target.view(-1))
    
    # Backward pass
    optimizer.zero_grad()
    loss.backward()
    optimizer.step()
    
    print(f"Training loss: {loss.item():.4f}")
    
    print("\n--- Model Summary ---")
    print(f"Total parameters: {sum(p.numel() for p in model.parameters()):,}")
    print(f"Trainable parameters: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")


if __name__ == "__main__":
    main()
