#!/usr/bin/env python3
"""
Advanced HyperConformer Example with Training Loop and Evaluation
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional
import time
import os
from hyperconformer_example import HyperConformer, create_padding_mask


class SyntheticSpeechDataset(Dataset):
    """Synthetic dataset for demonstration purposes."""
    
    def __init__(self, num_samples: int = 1000, seq_len_range: Tuple[int, int] = (50, 200), 
                 input_dim: int = 80, vocab_size: int = 1000):
        self.num_samples = num_samples
        self.seq_len_range = seq_len_range
        self.input_dim = input_dim
        self.vocab_size = vocab_size
        
        # Generate synthetic data
        self.data = []
        for _ in range(num_samples):
            seq_len = np.random.randint(seq_len_range[0], seq_len_range[1])
            
            # Synthetic mel-spectrogram features
            features = torch.randn(seq_len, input_dim)
            
            # Synthetic target sequence (phonemes/characters)
            target_len = max(1, seq_len // 4)  # Roughly 4:1 compression ratio
            target = torch.randint(0, vocab_size, (target_len,))
            
            self.data.append({
                'features': features,
                'target': target,
                'feature_length': seq_len,
                'target_length': target_len
            })
    
    def __len__(self):
        return self.num_samples
    
    def __getitem__(self, idx):
        return self.data[idx]


def collate_fn(batch: List[Dict]) -> Dict[str, torch.Tensor]:
    """Collate function for batching variable-length sequences."""
    
    # Get maximum lengths
    max_feature_len = max(item['feature_length'] for item in batch)
    max_target_len = max(item['target_length'] for item in batch)
    
    batch_size = len(batch)
    input_dim = batch[0]['features'].shape[1]
    
    # Initialize tensors
    features = torch.zeros(batch_size, max_feature_len, input_dim)
    targets = torch.full((batch_size, max_target_len), -1, dtype=torch.long)
    feature_lengths = torch.zeros(batch_size, dtype=torch.long)
    target_lengths = torch.zeros(batch_size, dtype=torch.long)
    
    # Fill tensors
    for i, item in enumerate(batch):
        feat_len = item['feature_length']
        tgt_len = item['target_length']
        
        features[i, :feat_len] = item['features']
        targets[i, :tgt_len] = item['target']
        feature_lengths[i] = feat_len
        target_lengths[i] = tgt_len
    
    return {
        'features': features,
        'targets': targets,
        'feature_lengths': feature_lengths,
        'target_lengths': target_lengths
    }


class HyperConformerTrainer:
    """Training and evaluation utilities for HyperConformer."""
    
    def __init__(self, model: HyperConformer, device: str = 'cuda' if torch.cuda.is_available() else 'cpu'):
        self.model = model.to(device)
        self.device = device
        self.train_losses = []
        self.val_losses = []
        
    def train_epoch(self, dataloader: DataLoader, optimizer: optim.Optimizer, 
                   criterion: nn.Module) -> float:
        """Train for one epoch."""
        self.model.train()
        total_loss = 0.0
        num_batches = 0
        
        for batch in dataloader:
            # Move to device
            features = batch['features'].to(self.device)
            targets = batch['targets'].to(self.device)
            feature_lengths = batch['feature_lengths'].to(self.device)
            
            # Create padding mask
            mask = create_padding_mask(feature_lengths, features.size(1)).to(self.device)
            
            # Forward pass
            optimizer.zero_grad()
            outputs = self.model(features, mask)
            
            # Compute loss (flatten for CrossEntropyLoss)
            loss = criterion(outputs.view(-1, outputs.size(-1)), targets.view(-1))
            
            # Backward pass
            loss.backward()
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            optimizer.step()
            
            total_loss += loss.item()
            num_batches += 1
        
        avg_loss = total_loss / num_batches
        self.train_losses.append(avg_loss)
        return avg_loss
    
    def evaluate(self, dataloader: DataLoader, criterion: nn.Module) -> float:
        """Evaluate the model."""
        self.model.eval()
        total_loss = 0.0
        num_batches = 0
        
        with torch.no_grad():
            for batch in dataloader:
                # Move to device
                features = batch['features'].to(self.device)
                targets = batch['targets'].to(self.device)
                feature_lengths = batch['feature_lengths'].to(self.device)
                
                # Create padding mask
                mask = create_padding_mask(feature_lengths, features.size(1)).to(self.device)
                
                # Forward pass
                outputs = self.model(features, mask)
                
                # Compute loss
                loss = criterion(outputs.view(-1, outputs.size(-1)), targets.view(-1))
                
                total_loss += loss.item()
                num_batches += 1
        
        avg_loss = total_loss / num_batches
        self.val_losses.append(avg_loss)
        return avg_loss
    
    def train(self, train_loader: DataLoader, val_loader: DataLoader, 
              num_epochs: int, learning_rate: float = 1e-4) -> None:
        """Complete training loop."""
        
        optimizer = optim.Adam(self.model.parameters(), lr=learning_rate, 
                              betas=(0.9, 0.98), eps=1e-9)
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', 
                                                        factor=0.5, patience=3)
        criterion = nn.CrossEntropyLoss(ignore_index=-1)
        
        print(f"Training on {self.device}")
        print(f"Model parameters: {sum(p.numel() for p in self.model.parameters()):,}")
        
        for epoch in range(num_epochs):
            start_time = time.time()
            
            # Train
            train_loss = self.train_epoch(train_loader, optimizer, criterion)
            
            # Evaluate
            val_loss = self.evaluate(val_loader, criterion)
            
            # Update learning rate
            scheduler.step(val_loss)
            
            epoch_time = time.time() - start_time
            
            print(f"Epoch {epoch+1}/{num_epochs} | "
                  f"Train Loss: {train_loss:.4f} | "
                  f"Val Loss: {val_loss:.4f} | "
                  f"Time: {epoch_time:.2f}s | "
                  f"LR: {optimizer.param_groups[0]['lr']:.2e}")
    
    def plot_losses(self, save_path: Optional[str] = None) -> None:
        """Plot training and validation losses."""
        plt.figure(figsize=(10, 6))
        plt.plot(self.train_losses, label='Training Loss', color='blue')
        plt.plot(self.val_losses, label='Validation Loss', color='red')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.title('HyperConformer Training Progress')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
    
    def save_model(self, path: str) -> None:
        """Save model checkpoint."""
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'train_losses': self.train_losses,
            'val_losses': self.val_losses
        }, path)
        print(f"Model saved to {path}")
    
    def load_model(self, path: str) -> None:
        """Load model checkpoint."""
        checkpoint = torch.load(path, map_location=self.device)
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.train_losses = checkpoint.get('train_losses', [])
        self.val_losses = checkpoint.get('val_losses', [])
        print(f"Model loaded from {path}")


def main():
    """Advanced training example."""
    
    # Configuration
    config = {
        'input_dim': 80,
        'd_model': 256,      # Smaller for faster training
        'num_layers': 6,     # Fewer layers for demo
        'num_heads': 8,
        'conv_kernel_size': 31,
        'expansion_factor': 4,
        'num_classes': 100,  # Smaller vocabulary
        'dropout': 0.1,
        'max_len': 1000
    }
    
    # Create datasets
    print("Creating datasets...")
    train_dataset = SyntheticSpeechDataset(num_samples=800, vocab_size=config['num_classes'])
    val_dataset = SyntheticSpeechDataset(num_samples=200, vocab_size=config['num_classes'])
    
    # Create data loaders
    train_loader = DataLoader(train_dataset, batch_size=8, shuffle=True, 
                             collate_fn=collate_fn, num_workers=0)
    val_loader = DataLoader(val_dataset, batch_size=8, shuffle=False, 
                           collate_fn=collate_fn, num_workers=0)
    
    # Create model and trainer
    print("Creating model...")
    model = HyperConformer(**config)
    trainer = HyperConformerTrainer(model)
    
    # Train model
    print("Starting training...")
    trainer.train(train_loader, val_loader, num_epochs=10, learning_rate=1e-4)
    
    # Plot results
    trainer.plot_losses('training_progress.png')
    
    # Save model
    os.makedirs('checkpoints', exist_ok=True)
    trainer.save_model('checkpoints/hyperconformer_demo.pth')
    
    # Inference example
    print("\n--- Inference Example ---")
    model.eval()
    with torch.no_grad():
        # Single sample inference
        sample_input = torch.randn(1, 150, config['input_dim'])
        sample_lengths = torch.tensor([150])
        sample_mask = create_padding_mask(sample_lengths, 150)
        
        output = model(sample_input, sample_mask)
        predictions = torch.argmax(output, dim=-1)
        
        print(f"Input shape: {sample_input.shape}")
        print(f"Output shape: {output.shape}")
        print(f"Predicted tokens: {predictions[0, :20].tolist()}")  # First 20 predictions
    
    print("\nTraining completed successfully!")


if __name__ == "__main__":
    main()
