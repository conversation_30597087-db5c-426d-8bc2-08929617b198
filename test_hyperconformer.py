#!/usr/bin/env python3
"""
Simple test script for HyperConformer implementation
"""

import torch
import sys

def test_basic_functionality():
    """Test basic HyperConformer functionality."""
    
    try:
        from hyperconformer_example import HyperConformer, create_padding_mask
        print("✓ Successfully imported HyperConformer")
        
        # Test model creation
        model = HyperConformer(
            input_dim=80,
            d_model=256,  # Smaller for testing
            num_layers=2,  # Fewer layers for testing
            num_heads=4,
            num_classes=100
        )
        print("✓ Successfully created HyperConformer model")
        
        # Test forward pass
        batch_size, seq_len, input_dim = 2, 50, 80
        x = torch.randn(batch_size, seq_len, input_dim)
        lengths = torch.tensor([50, 40])
        mask = create_padding_mask(lengths, seq_len)
        
        print(f"✓ Created test input: {x.shape}")
        
        # Forward pass
        model.eval()
        with torch.no_grad():
            output = model(x, mask)
        
        print(f"✓ Forward pass successful: {output.shape}")
        print(f"✓ Model parameters: {sum(p.numel() for p in model.parameters()):,}")
        
        # Test training mode
        model.train()
        optimizer = torch.optim.Adam(model.parameters(), lr=1e-4)
        criterion = torch.nn.CrossEntropyLoss(ignore_index=-1)
        
        # Dummy target
        target = torch.randint(0, 100, (batch_size, seq_len))
        
        # Training step
        optimizer.zero_grad()
        output = model(x, mask)
        loss = criterion(output.view(-1, output.size(-1)), target.view(-1))
        loss.backward()
        optimizer.step()
        
        print(f"✓ Training step successful, loss: {loss.item():.4f}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_components():
    """Test individual components."""
    
    try:
        from hyperconformer_example import (
            MultiHeadAttention, HyperMixer, ConvolutionModule, 
            HyperConformerBlock, PositionalEncoding
        )
        
        d_model = 256
        seq_len = 50
        batch_size = 2
        
        x = torch.randn(batch_size, seq_len, d_model)
        
        # Test PositionalEncoding
        pos_enc = PositionalEncoding(d_model)
        x_pos = pos_enc(x.transpose(0, 1)).transpose(0, 1)
        print(f"✓ PositionalEncoding: {x_pos.shape}")
        
        # Test MultiHeadAttention
        mha = MultiHeadAttention(d_model, num_heads=8)
        attn_out = mha(x)
        print(f"✓ MultiHeadAttention: {attn_out.shape}")
        
        # Test HyperMixer
        mixer = HyperMixer(d_model)
        mixer_out = mixer(x)
        print(f"✓ HyperMixer: {mixer_out.shape}")
        
        # Test ConvolutionModule
        conv = ConvolutionModule(d_model)
        conv_out = conv(x)
        print(f"✓ ConvolutionModule: {conv_out.shape}")
        
        # Test HyperConformerBlock
        block = HyperConformerBlock(d_model, num_heads=8)
        block_out = block(x)
        print(f"✓ HyperConformerBlock: {block_out.shape}")
        
        return True
        
    except Exception as e:
        print(f"✗ Component test error: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_config():
    """Test configuration system."""
    
    try:
        from config import get_config, print_config
        
        config = get_config('small')
        print("✓ Successfully loaded config")
        
        print("\nConfiguration details:")
        print_config(config)
        
        return True
        
    except Exception as e:
        print(f"✗ Config test error: {e}")
        return False


def main():
    """Run all tests."""
    
    print("HyperConformer Implementation Test")
    print("=" * 40)
    
    print("\n1. Testing basic functionality...")
    basic_ok = test_basic_functionality()
    
    print("\n2. Testing individual components...")
    components_ok = test_components()
    
    print("\n3. Testing configuration system...")
    config_ok = test_config()
    
    print("\n" + "=" * 40)
    if basic_ok and components_ok and config_ok:
        print("✓ All tests passed! HyperConformer implementation is working correctly.")
        return 0
    else:
        print("✗ Some tests failed. Please check the errors above.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
