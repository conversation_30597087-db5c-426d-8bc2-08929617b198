#!/usr/bin/env python3
"""
Simple script to run HyperConformer examples
"""

import argparse
import torch
from config import get_config, print_config


def run_basic_example():
    """Run the basic HyperConformer example."""
    print("Running basic HyperConformer example...")
    from hyperconformer_example import main
    main()


def run_advanced_example():
    """Run the advanced training example."""
    print("Running advanced HyperConformer training example...")
    from hyperconformer_advanced_example import main
    main()


def run_inference_example():
    """Run inference example with pretrained model."""
    from hyperconformer_example import HyperConformer, create_padding_mask
    
    print("Running inference example...")
    
    # Load configuration
    config = get_config('small')  # Use small config for demo
    print_config(config)
    
    # Create model
    model = HyperConformer(
        input_dim=config.input_dim,
        d_model=config.d_model,
        num_layers=config.num_layers,
        num_heads=config.num_heads,
        conv_kernel_size=config.conv_kernel_size,
        expansion_factor=config.expansion_factor,
        num_classes=config.num_classes,
        dropout=config.dropout,
        max_len=config.max_len
    )
    
    # Set to evaluation mode
    model.eval()
    
    # Create sample input
    batch_size, seq_len = 2, 100
    x = torch.randn(batch_size, seq_len, config.input_dim)
    lengths = torch.tensor([100, 80])  # Variable lengths
    mask = create_padding_mask(lengths, seq_len)
    
    print(f"\nInput shape: {x.shape}")
    print(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # Run inference
    with torch.no_grad():
        start_time = torch.cuda.Event(enable_timing=True) if torch.cuda.is_available() else None
        end_time = torch.cuda.Event(enable_timing=True) if torch.cuda.is_available() else None
        
        if start_time:
            start_time.record()
        
        output = model(x, mask)
        
        if end_time:
            end_time.record()
            torch.cuda.synchronize()
            inference_time = start_time.elapsed_time(end_time)
            print(f"Inference time: {inference_time:.2f} ms")
    
    print(f"Output shape: {output.shape}")
    print(f"Output range: [{output.min().item():.3f}, {output.max().item():.3f}]")
    
    # Get predictions
    predictions = torch.argmax(output, dim=-1)
    print(f"Sample predictions: {predictions[0, :10].tolist()}")


def benchmark_model():
    """Benchmark model performance with different configurations."""
    print("Benchmarking HyperConformer performance...")
    
    from hyperconformer_example import HyperConformer
    import time
    
    configs = ['small', 'medium', 'large']
    batch_size, seq_len = 4, 200
    
    for config_name in configs:
        config = get_config(config_name)
        
        print(f"\n--- {config_name.upper()} Configuration ---")
        print(f"d_model: {config.d_model}, layers: {config.num_layers}")
        
        # Create model
        model = HyperConformer(
            input_dim=config.input_dim,
            d_model=config.d_model,
            num_layers=config.num_layers,
            num_heads=config.num_heads,
            num_classes=config.num_classes
        )
        
        model.eval()
        
        # Create input
        x = torch.randn(batch_size, seq_len, config.input_dim)
        
        # Warmup
        with torch.no_grad():
            for _ in range(5):
                _ = model(x)
        
        # Benchmark
        times = []
        with torch.no_grad():
            for _ in range(20):
                start = time.time()
                _ = model(x)
                times.append(time.time() - start)
        
        avg_time = sum(times) / len(times)
        params = sum(p.numel() for p in model.parameters())
        
        print(f"Parameters: {params:,}")
        print(f"Average inference time: {avg_time*1000:.2f} ms")
        print(f"Throughput: {batch_size/avg_time:.1f} samples/sec")


def main():
    parser = argparse.ArgumentParser(description='Run HyperConformer examples')
    parser.add_argument('--mode', type=str, default='basic',
                       choices=['basic', 'advanced', 'inference', 'benchmark'],
                       help='Example mode to run')
    parser.add_argument('--config', type=str, default='medium',
                       choices=['small', 'medium', 'large', 'english_asr', 'multilingual'],
                       help='Model configuration to use')
    
    args = parser.parse_args()
    
    print("HyperConformer Example Runner")
    print("=" * 40)
    
    if args.mode == 'basic':
        run_basic_example()
    elif args.mode == 'advanced':
        run_advanced_example()
    elif args.mode == 'inference':
        run_inference_example()
    elif args.mode == 'benchmark':
        benchmark_model()
    else:
        print(f"Unknown mode: {args.mode}")


if __name__ == "__main__":
    main()
