#!/usr/bin/env python3
"""
Configuration file for HyperConformer model
"""

from dataclasses import dataclass
from typing import Optional


@dataclass
class HyperConformerConfig:
    """Configuration class for HyperConformer model."""
    
    # Model architecture
    input_dim: int = 80              # Input feature dimension (e.g., mel-spectrogram)
    d_model: int = 512               # Model hidden dimension
    num_layers: int = 12             # Number of HyperConformer blocks
    num_heads: int = 8               # Number of attention heads
    conv_kernel_size: int = 31       # Convolution kernel size
    expansion_factor: int = 4        # Feed-forward expansion factor
    num_classes: int = 1000          # Output vocabulary size
    dropout: float = 0.1             # Dropout rate
    max_len: int = 5000              # Maximum sequence length
    
    # Training parameters
    batch_size: int = 16             # Training batch size
    learning_rate: float = 1e-4      # Initial learning rate
    num_epochs: int = 100            # Number of training epochs
    warmup_steps: int = 4000         # Learning rate warmup steps
    weight_decay: float = 1e-6       # Weight decay for regularization
    grad_clip_norm: float = 1.0      # Gradient clipping norm
    
    # Data parameters
    sample_rate: int = 16000         # Audio sample rate
    n_mels: int = 80                 # Number of mel-spectrogram bins
    n_fft: int = 512                 # FFT size
    hop_length: int = 160            # Hop length for STFT
    win_length: int = 400            # Window length for STFT
    
    # Optimization
    optimizer: str = 'adam'          # Optimizer type
    scheduler: str = 'plateau'       # Learning rate scheduler
    patience: int = 5                # Scheduler patience
    factor: float = 0.5              # Scheduler factor
    
    # Paths
    data_path: Optional[str] = None  # Path to training data
    checkpoint_path: str = 'checkpoints'  # Path to save checkpoints
    log_path: str = 'logs'           # Path to save logs
    
    # Hardware
    device: str = 'auto'             # Device to use ('auto', 'cpu', 'cuda')
    num_workers: int = 4             # Number of data loader workers
    
    # Logging
    log_interval: int = 100          # Steps between logging
    save_interval: int = 1000        # Steps between saving checkpoints
    eval_interval: int = 500         # Steps between evaluation


# Predefined configurations for different use cases

SMALL_CONFIG = HyperConformerConfig(
    d_model=256,
    num_layers=6,
    num_heads=4,
    batch_size=32,
    num_classes=100
)

MEDIUM_CONFIG = HyperConformerConfig(
    d_model=512,
    num_layers=12,
    num_heads=8,
    batch_size=16,
    num_classes=1000
)

LARGE_CONFIG = HyperConformerConfig(
    d_model=768,
    num_layers=18,
    num_heads=12,
    batch_size=8,
    num_classes=5000
)

# Configuration for different languages/tasks
ENGLISH_ASR_CONFIG = HyperConformerConfig(
    num_classes=28,  # 26 letters + space + blank
    d_model=512,
    num_layers=12
)

MULTILINGUAL_CONFIG = HyperConformerConfig(
    num_classes=1000,  # Larger vocabulary for multiple languages
    d_model=768,
    num_layers=18
)


def get_config(config_name: str = 'medium') -> HyperConformerConfig:
    """Get predefined configuration by name."""
    configs = {
        'small': SMALL_CONFIG,
        'medium': MEDIUM_CONFIG,
        'large': LARGE_CONFIG,
        'english_asr': ENGLISH_ASR_CONFIG,
        'multilingual': MULTILINGUAL_CONFIG
    }
    
    if config_name not in configs:
        raise ValueError(f"Unknown config: {config_name}. Available: {list(configs.keys())}")
    
    return configs[config_name]


def print_config(config: HyperConformerConfig) -> None:
    """Print configuration details."""
    print("HyperConformer Configuration:")
    print("=" * 40)
    
    print("Model Architecture:")
    print(f"  Input dimension: {config.input_dim}")
    print(f"  Model dimension: {config.d_model}")
    print(f"  Number of layers: {config.num_layers}")
    print(f"  Number of heads: {config.num_heads}")
    print(f"  Convolution kernel size: {config.conv_kernel_size}")
    print(f"  Expansion factor: {config.expansion_factor}")
    print(f"  Output classes: {config.num_classes}")
    print(f"  Dropout rate: {config.dropout}")
    
    print("\nTraining Parameters:")
    print(f"  Batch size: {config.batch_size}")
    print(f"  Learning rate: {config.learning_rate}")
    print(f"  Number of epochs: {config.num_epochs}")
    print(f"  Weight decay: {config.weight_decay}")
    print(f"  Gradient clipping: {config.grad_clip_norm}")
    
    print("\nData Parameters:")
    print(f"  Sample rate: {config.sample_rate}")
    print(f"  Mel bins: {config.n_mels}")
    print(f"  FFT size: {config.n_fft}")
    print(f"  Hop length: {config.hop_length}")
    
    # Estimate model parameters
    # Rough estimation based on transformer architecture
    embed_params = config.input_dim * config.d_model
    attention_params = config.num_layers * config.num_heads * config.d_model * config.d_model * 4
    ff_params = config.num_layers * config.d_model * config.d_model * config.expansion_factor * 2
    output_params = config.d_model * config.num_classes
    
    total_params = embed_params + attention_params + ff_params + output_params
    
    print(f"\nEstimated Parameters: ~{total_params:,}")
    print("=" * 40)


if __name__ == "__main__":
    # Example usage
    config = get_config('medium')
    print_config(config)
    
    print("\nAvailable configurations:")
    for name in ['small', 'medium', 'large', 'english_asr', 'multilingual']:
        cfg = get_config(name)
        print(f"  {name}: {cfg.d_model}d, {cfg.num_layers}L, {cfg.num_classes} classes")
