# HyperConformer 使用指南

这是一个完整可运行的 HyperConformer Python 实现，基于论文 "HyperConformer: Multi-head HyperMixer for Efficient Speech Recognition"。

## 快速开始

### 1. 基本使用

```bash
# 运行基本示例
python hyperconformer_example.py

# 或使用运行脚本
python run_example.py --mode basic
```

### 2. 高级训练示例

```bash
# 运行完整的训练示例（包含合成数据）
python hyperconformer_advanced_example.py

# 或使用运行脚本
python run_example.py --mode advanced
```

### 3. 推理示例

```bash
# 运行推理示例
python run_example.py --mode inference
```

### 4. 性能基准测试

```bash
# 测试不同配置的性能
python run_example.py --mode benchmark
```

## 代码结构说明

### 核心文件

1. **`hyperconformer_example.py`** - 基本实现
   - `HyperConformer`: 主模型类
   - `HyperConformerBlock`: 单个 HyperConformer 块
   - `HyperMixer`: 核心 HyperMixer 模块
   - `MultiHeadAttention`: 多头注意力机制
   - `ConvolutionModule`: 卷积模块

2. **`hyperconformer_advanced_example.py`** - 高级训练示例
   - `HyperConformerTrainer`: 训练和评估工具
   - `SyntheticSpeechDataset`: 合成语音数据集
   - 完整的训练循环和可视化

3. **`config.py`** - 配置管理
   - 预定义的模型配置
   - 训练参数设置
   - 配置工具函数

4. **`run_example.py`** - 便捷运行脚本
   - 统一的入口点
   - 多种运行模式
   - 基准测试功能

## 模型配置

### 预定义配置

| 配置名称 | 参数量 | d_model | 层数 | 用途 |
|---------|--------|---------|------|------|
| small | ~17M | 256 | 6 | 开发/测试 |
| medium | ~136M | 512 | 12 | 通用ASR |
| large | ~461M | 768 | 18 | 高精度ASR |

### 使用配置

```python
from config import get_config, print_config

# 加载预定义配置
config = get_config('medium')
print_config(config)

# 创建模型
from hyperconformer_example import HyperConformer
model = HyperConformer(**config.__dict__)
```

## 自定义使用

### 1. 创建自定义模型

```python
from hyperconformer_example import HyperConformer

# 自定义配置
model = HyperConformer(
    input_dim=80,        # 输入特征维度（如梅尔频谱图）
    d_model=512,         # 模型隐藏维度
    num_layers=12,       # HyperConformer 块数量
    num_heads=8,         # 注意力头数
    conv_kernel_size=31, # 卷积核大小
    expansion_factor=4,  # 前馈网络扩展因子
    num_classes=1000,    # 输出词汇表大小
    dropout=0.1          # Dropout 率
)
```

### 2. 训练自定义模型

```python
from hyperconformer_advanced_example import HyperConformerTrainer

# 创建训练器
trainer = HyperConformerTrainer(model)

# 训练模型
trainer.train(
    train_loader=train_loader,
    val_loader=val_loader,
    num_epochs=50,
    learning_rate=1e-4
)

# 保存模型
trainer.save_model('my_model.pth')
```

### 3. 推理使用

```python
import torch
from hyperconformer_example import create_padding_mask

# 准备输入数据
# x: (batch_size, seq_len, input_dim)
x = torch.randn(1, 100, 80)  # 1个样本，100帧，80维特征
lengths = torch.tensor([100])
mask = create_padding_mask(lengths, 100)

# 推理
model.eval()
with torch.no_grad():
    output = model(x, mask)
    predictions = torch.argmax(output, dim=-1)

print(f"预测结果: {predictions[0, :10].tolist()}")
```

## 架构特点

### 1. HyperMixer 模块
HyperConformer 的核心创新，使用超网络生成动态混合权重：

```python
# 使用超网络生成混合权重
hyper_weights = self.hyper_net(x.mean(dim=1, keepdim=True))
weight_1, weight_2 = hyper_weights.chunk(2, dim=-1)

# 应用动态权重进行混合
mixed = self.mixer(x)
output = weight_1 * x + weight_2 * mixed
```

### 2. 卷积模块
高效的卷积处理，包含：
- 逐点卷积
- 深度可分离卷积
- 门控线性单元 (GLU)
- Swish 激活函数

### 3. 多头注意力
并行的注意力机制，用于更好的表示学习。

## 性能基准

在合成数据上的基准测试结果（CPU推理）：

| 配置 | 参数量 | 推理时间 | 吞吐量 |
|------|--------|----------|--------|
| Small | 17M | ~86ms | 46 samples/sec |
| Medium | 136M | ~518ms | 8 samples/sec |
| Large | 461M | ~1483ms | 3 samples/sec |

*结果可能因硬件和序列长度而异*

## 实际应用建议

### 1. 语音识别任务

```python
# 英语ASR配置
config = get_config('english_asr')  # 28个类别（字母+空格+空白）

# 多语言ASR配置
config = get_config('multilingual')  # 1000个类别
```

### 2. 训练技巧

- 使用梯度裁剪防止梯度爆炸
- 采用学习率调度器
- 使用混合精度训练加速
- 适当的批大小设置

### 3. 推理优化

- 使用 GPU 进行推理
- 批处理多个样本
- 考虑模型量化
- 使用 TorchScript 进行部署

## 扩展和自定义

### 1. 添加新组件

```python
class CustomHyperMixer(nn.Module):
    """自定义 HyperMixer 实现"""
    
    def __init__(self, d_model, custom_param=None):
        super().__init__()
        # 你的自定义实现
        
    def forward(self, x):
        # 你的自定义前向传播
        return x
```

### 2. 自定义训练循环

```python
# 创建自定义训练配置
from config import HyperConformerConfig

custom_config = HyperConformerConfig(
    d_model=384,
    num_layers=8,
    num_heads=6,
    batch_size=24,
    learning_rate=5e-4
)
```

## 故障排除

### 常见问题

1. **CUDA 内存不足**: 减少批大小或模型大小
2. **训练缓慢**: 启用混合精度训练或使用更小的模型
3. **导入错误**: 确保所有依赖项已安装

### 性能优化建议

1. 训练时使用 GPU
2. 启用混合精度训练以加快训练速度
3. 为你的硬件选择合适的批大小
4. 考虑梯度累积以获得更大的有效批大小

## 测试

运行测试脚本验证实现：

```bash
python test_hyperconformer.py
```

这将测试：
- 基本功能
- 各个组件
- 配置系统

## 总结

这个 HyperConformer 实现提供了：

✅ 完整的模型架构实现  
✅ 灵活的配置系统  
✅ 训练和评估工具  
✅ 推理示例  
✅ 性能基准测试  
✅ 详细的文档和示例  

你可以直接使用这些脚本进行实验，或者作为你自己项目的起点。
